import type { FeedbackTriggerSearchParam, FeedbackTriggerSolution, FeedbackTriggerSolutionCreateParam, FeedbackTriggerUpdateParam } from './type'
import type { PageList, Pageable } from '~/api/common/type'
import { kyGet, kyPost, kyPut } from '~/utils/request'

export const SolutionApi = {
  getByTriggerRecordAndSend: (param: FeedbackTriggerSearchParam) =>
    kyGet('feedback-trigger-solutions/by-trigger-record-and-send', param).json<FeedbackTriggerSolution>(),
  create: (param: FeedbackTriggerSolutionCreateParam) => kyPost('feedback-trigger-solutions', param),
  update: (id: string, param: Partial<FeedbackTriggerUpdateParam>) => kyPut(`feedback-trigger-solutions/${id}`, param),
  submit: (id: string) => kyPut(`feedback-trigger-solutions/${id}/submit`).json<FeedbackTriggerSolution>(),
  page: (param: Pageable<Partial<FeedbackTriggerSolution>>) =>
    kyPost('feedback-trigger-solutions/page', param).json<PageList<FeedbackTriggerSolution>>(),
}
